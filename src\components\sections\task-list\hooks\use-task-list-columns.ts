// src/components/sections/task-list/hooks/use-task-list-columns.ts

import { useMemo, useCallback } from 'react';
import type { ColumnDef, CellContext } from '@tanstack/react-table';
import { cn } from '@/lib/utils';
import type { Task, Vehicle, StyleableColumnId, DensityStyleValues } from '@/types';
import { ALL_TASK_COLUMNS_CONFIG } from '../task-list.config';
import { getStyleableColumnId, getTask, getTaskWithVehicles } from '../task-list-type-guards';
import { useTaskListSettings } from '@/hooks/useTaskListSettings';
import { useReminderStore } from '@/store/reminderStore';
import { allTextColorOptionsMap } from '@/components/modals/column-specific-style-modal';

// Cell components
import { DispatchReminderCell } from '../cells/DispatchReminderCell';
import { TaskProgressCell } from '../cells/TaskProgressCell';
import { DispatchedVehiclesCell } from '../cells/DispatchedVehiclesCell';
import { ProductionLinesCell } from '../cells/ProductionLinesCell';
import { MessageCell } from '../cells/MessageCell';

interface UseTaskListColumnsProps {
  densityStyles: DensityStyleValues;
  vehicleDisplayMode: 'licensePlate' | 'internalId';
  inTaskVehicleCardStyles: any;
  productionLineCount: number;
  onDropOnProductionLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onOpenStyleEditor: () => void;
  onOpenDeliveryOrderDetails: (vehicle: Vehicle, task: Task) => void;
  onOpenVehicleCardContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  getColumnBackgroundProps: (columnId: string, isHeader: boolean, isFixed: boolean) => { style: React.CSSProperties, className: string };
}

/**
 * 任务列表表格列配置Hook
 * 负责生成和管理表格列的配置
 */
export function useTaskListColumns({
  densityStyles,
  vehicleDisplayMode,
  inTaskVehicleCardStyles,
  productionLineCount,
  onDropOnProductionLine,
  onCancelVehicleDispatch,
  onOpenStyleEditor,
  onOpenDeliveryOrderDetails,
  onOpenVehicleCardContextMenu,
  getColumnBackgroundProps,
}: UseTaskListColumnsProps) {
  const { settings } = useTaskListSettings();
  const { messages, markAsRead } = useReminderStore();

  // 获取单元格文本样式
  const getCellTextClasses = useCallback((columnId: StyleableColumnId): string => {
    const styles = settings.columnTextStyles[columnId];
    const classes: string[] = [];
    
    if (styles?.color && styles.color !== 'default') {
      const colorOption = allTextColorOptionsMap.get(styles.color);
      classes.push(colorOption ? colorOption.className : 'text-foreground');
    } else {
      classes.push('text-foreground');
    }
    
    if (styles?.fontSize && styles.fontSize !== 'default') {
      classes.push(styles.fontSize);
    } else {
      classes.push(densityStyles.cellFontSize);
    }

    if (styles?.fontWeight && styles.fontWeight !== 'default') {
      classes.push(styles.fontWeight);
    } else {
      classes.push(densityStyles.cellFontWeight);
    }

    return cn(classes);
  }, [settings.columnTextStyles, densityStyles]);

  // Memoize column metadata separately to reduce re-renders
  const columnMeta = useMemo(() => ({
    getColumnBackgroundProps,
    densityStyles
  }), [getColumnBackgroundProps, densityStyles]);

  // Memoize cell renderers that don't depend on dynamic data
  const cellRenderers = useMemo(() => ({
    dispatchReminder: (task: Task, columnId: StyleableColumnId) => (
      <DispatchReminderCell task={task} textClassName={getCellTextClasses(columnId)} />
    ),
    messages: (task: Task, columnId: StyleableColumnId) => {
      const taskMessages = messages.filter(msg => msg.taskId === task.id);
      const unreadCount = taskMessages.filter(msg => !msg.read).length;
      return (
        <MessageCell
          task={task}
          taskMessages={taskMessages}
          unreadCount={unreadCount}
          onMarkAsRead={markAsRead}
          textClassName={getCellTextClasses(columnId)}
        />
      );
    },
    completedProgress: (task: Task, columnId: StyleableColumnId) => {
      const progressValue = task.requiredVolume > 0 ? (task.completedVolume / task.requiredVolume) * 100 : 0;
      return <TaskProgressCell progressValue={progressValue} textClassName={getCellTextClasses(columnId)} />;
    },
    dispatchedVehicles: (task: Task) => (
      <DispatchedVehiclesCell
        task={task}
        taskVehicles={getTaskWithVehicles(task)?.vehicles || []}
        vehicleDisplayMode={vehicleDisplayMode}
        inTaskVehicleCardStyles={inTaskVehicleCardStyles}
        productionLineCount={productionLineCount}
        density={settings.density}
        onCancelDispatch={onCancelVehicleDispatch}
        onOpenStyleEditor={onOpenStyleEditor}
        onOpenDeliveryOrderDetails={(vehicle, currentTask) => onOpenDeliveryOrderDetails(vehicle, currentTask)}
        onOpenContextMenu={(e, vehicle, currentTask) => onOpenVehicleCardContextMenu(e, vehicle, currentTask)}
      />
    ),
    productionLines: (task: Task) => (
      <ProductionLinesCell
        task={task}
        productionLineCount={productionLineCount}
        densityStyles={densityStyles}
        onDropVehicleOnLine={onDropOnProductionLine}
      />
    ),
    supplyTime: (task: Task, columnId: StyleableColumnId) => {
      const combinedSupplyTime = `${task.supplyDate || ''} ${task.supplyTime || ''}`.trim();
      return (
        <span
          className={cn("truncate block w-full h-full", getCellTextClasses(columnId))}
          title={combinedSupplyTime}
        >
          {combinedSupplyTime}
        </span>
      );
    },
    default: (value: any, columnId: StyleableColumnId) => (
      <span className={cn("truncate block w-full h-full", getCellTextClasses(columnId))}>
        {String(value ?? '')}
      </span>
    )
  }), [
    getCellTextClasses, messages, markAsRead, vehicleDisplayMode, inTaskVehicleCardStyles,
    productionLineCount, settings.density, densityStyles, onCancelVehicleDispatch,
    onOpenStyleEditor, onOpenDeliveryOrderDetails, onOpenVehicleCardContextMenu,
    onDropOnProductionLine
  ]);

  // 生成表格列配置
  const tableColumns = useMemo<ColumnDef<Task>[]>(() => {
    return Array.from(ALL_TASK_COLUMNS_CONFIG).map(colDef => {
      const baseCol: ColumnDef<Task> = {
        id: colDef.id,
        accessorKey: colDef.id,
        header: () => colDef.label,
        size: settings.columnWidths[colDef.id] || colDef.defaultWidth || 150,
        minSize: colDef.id === 'dispatchedVehicles' ? 200 : (colDef.minWidth || 50),
        maxSize: colDef.id === 'dispatchedVehicles' ? 600 : colDef.maxWidth,
        enableResizing: colDef.isResizable !== false,
        enableHiding: !colDef.isMandatory,
        meta: { customDef: { ...colDef, ...columnMeta } },
        cell: (info: CellContext<Task, any>) => {
          const task = info.row.original;
          const value = info.getValue();
          const columnId = getStyleableColumnId(info.column.id);

          switch (columnId) {
            case 'dispatchReminder':
              return cellRenderers.dispatchReminder(task, columnId);
            case 'messages':
              return cellRenderers.messages(task, columnId);
            case 'completedProgress':
              return cellRenderers.completedProgress(task, columnId);
            case 'dispatchedVehicles':
              return cellRenderers.dispatchedVehicles(task);
            case 'productionLines':
              return cellRenderers.productionLines(task);
            case 'supplyTime':
              return cellRenderers.supplyTime(task, columnId);
            default:
              return cellRenderers.default(value, columnId);
          }
        }
      };
      return {...baseCol}; // 确保返回新对象
    });
  }, [
    settings.columnWidths, // Affects column sizes
    columnMeta, // Affects meta data
    cellRenderers // Affects cell rendering
  ]);

  return {
    tableColumns,
    getCellTextClasses,
    cellRenderers,
  };
}
