// src/components/sections/task-list/components/task-list-card-content.tsx

import React from 'react';
import type { 
  Task, 
  Vehicle, 
  TaskListStoredSettings, 
  VehicleDisplayMode,
  TaskGroupConfig,
  TaskGroup
} from '@/types';
import { TaskCardConfig } from '@/types/taskCardConfig';
import { EnhancedTaskCardView } from '../EnhancedTaskCardView';

// Default card configuration
const defaultCardConfig = {
  size: 'small' as const,
  layout: 'compact' as const,
  theme: 'default' as const,
  spacing: 'normal' as const,
  borderRadius: 'medium' as const,
  shadow: 'small' as const,
  animation: 'subtle' as const,
  columns: 'auto' as const,
};

interface TaskListCardContentProps {
  // Data
  filteredTasks: Task[];
  vehicles: Vehicle[];
  taskGroups: TaskGroup[];
  
  // Settings
  settings: TaskListStoredSettings;
  vehicleDisplayMode: VehicleDisplayMode;
  taskStatusFilter: string;
  taskCardConfig: TaskCardConfig;
  
  // Drag and Drop State
  dragOverTaskId: string | null;
  setDragOverTaskId: (taskId: string | null) => void;
  dragOverProductionLineId: string | null;
  setDragOverProductionLineId: (lineId: string | null) => void;
  
  // Event Handlers
  handleVehicleDrop: (vehicle: Vehicle, taskId: string) => void;
  handleTaskContextMenu: (event: React.MouseEvent, task: Task) => void;
  handleRowDoubleClick: (task: Task) => void;
  onOpenVehicleCardContextMenu: (e: React.MouseEvent, vehicle: Vehicle, task: Task) => void;
  onOpenDeliveryOrderDetailsForVehicle: (vehicleId: string, taskId: string) => void;
  onOpenStyleEditor: () => void;
  onCancelVehicleDispatch: (vehicleId: string) => void;
  onVehicleDispatchedToLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onDropVehicleFromPanelOnTaskCard: (vehicle: Vehicle, taskId: string) => void;
  onDropVehicleOnLine: (vehicle: Vehicle, taskId: string, lineId: string) => void;
  onToggleGroupCollapse: (groupKey: string) => void;
  onCancelGrouping: () => void;
  onTaskCardConfigChange: (config: TaskCardConfig) => void;
  onOpenCardConfigModal: () => void;
  
  // Utility Functions
  getStatusLabelProps: (status: string) => { label: string; variant: string };
  
  className?: string;
}

export function TaskListCardContent({
  filteredTasks,
  vehicles,
  taskGroups,
  settings,
  vehicleDisplayMode,
  taskStatusFilter,
  taskCardConfig,
  dragOverTaskId,
  setDragOverTaskId,
  dragOverProductionLineId,
  setDragOverProductionLineId,
  handleVehicleDrop,
  handleTaskContextMenu,
  handleRowDoubleClick,
  onOpenVehicleCardContextMenu,
  onOpenDeliveryOrderDetailsForVehicle,
  onOpenStyleEditor,
  onCancelVehicleDispatch,
  onVehicleDispatchedToLine,
  onDropVehicleFromPanelOnTaskCard,
  onDropVehicleOnLine,
  onToggleGroupCollapse,
  onCancelGrouping,
  onTaskCardConfigChange,
  onOpenCardConfigModal,
  getStatusLabelProps,
  className
}: TaskListCardContentProps) {
  
  return (
    <div className={className}>
      <EnhancedTaskCardView
        filteredTasks={filteredTasks}
        vehicles={vehicles}
        settings={settings}
        productionLineCount={3} // This should be passed as a prop
        vehicleDisplayMode={vehicleDisplayMode}
        taskStatusFilter={taskStatusFilter}
        dragOverTaskId={dragOverTaskId}
        setDragOverTaskId={setDragOverTaskId}
        dragOverProductionLineId={dragOverProductionLineId}
        setDragOverProductionLineId={setDragOverProductionLineId}
        handleVehicleDrop={handleVehicleDrop}
        handleTaskContextMenu={handleTaskContextMenu}
        handleRowDoubleClick={handleRowDoubleClick}
        getStatusLabelProps={getStatusLabelProps}
        onOpenVehicleCardContextMenu={onOpenVehicleCardContextMenu}
        onOpenDeliveryOrderDetailsForVehicle={onOpenDeliveryOrderDetailsForVehicle}
        onOpenStyleEditor={onOpenStyleEditor}
        onCancelVehicleDispatch={onCancelVehicleDispatch}
        onVehicleDispatchedToLine={onVehicleDispatchedToLine}
        onDropVehicleFromPanelOnTaskCard={onDropVehicleFromPanelOnTaskCard}
        onDropVehicleOnLine={onDropVehicleOnLine}
        groupConfig={settings.groupConfig}
        onToggleGroupCollapse={onToggleGroupCollapse}
        onCancelGrouping={onCancelGrouping}
        taskCardConfig={taskCardConfig}
        onTaskCardConfigChange={onTaskCardConfigChange}
        onOpenCardConfigModal={onOpenCardConfigModal}
      />
    </div>
  );
}
