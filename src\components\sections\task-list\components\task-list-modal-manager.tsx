// src/components/sections/task-list/components/task-list-modal-manager.tsx

import React from 'react';
import type { 
  Task, 
  Vehicle, 
  CustomColumnDefinition, 
  ColumnTextStyle, 
  StyleableColumnId,
  InTaskVehicleCardStyle,
  TaskListStoredSettings,
  TaskGroupConfig
} from '@/types';
import { TaskCardConfig } from '@/types/taskCardConfig';
import { TaskListModals } from '../task-list-modals';
import { TaskCardConfigModal } from '../cards/TaskCardConfigModal';
import { TaskGroupConfigModal } from '../modals/task-group-config-modal';
import { TaskListContextMenus } from '../task-list-context-menus';

interface TaskListModalManagerProps {
  // Tanker Note Modal
  isTankerNoteModalOpen: boolean;
  closeTankerNoteModal: () => void;
  selectedTaskForTankerNote: Task | null;

  // Column Visibility Modal
  isColumnVisibilityModalOpen: boolean;
  closeColumnVisibilityModal: () => void;
  allColumns: CustomColumnDefinition[];
  columnVisibility: Record<string, boolean>;
  handleColumnVisibilityChange: (columnId: string, checked: boolean) => void;
  currentOrder: string[];
  handleColumnOrderChange: (newOrder: string[]) => void;

  // Column Specific Style Modal
  isColumnSpecificStyleModalOpen: boolean;
  closeColumnSpecificStyleModal: () => void;
  editingColumnDef: CustomColumnDefinition | null;
  columnTextStyles: Record<StyleableColumnId, ColumnTextStyle | undefined>;
  columnBackgrounds: Record<string, string>;
  handleColumnTextStyleChange: (columnId: StyleableColumnId, property: keyof ColumnTextStyle, value: string) => void;
  handleColumnBackgroundChange: (columnId: string, value: string) => void;

  // Vehicle Card Styler Modal
  isStyleEditorModalOpen: boolean;
  closeStyleEditorModal: () => void;
  inTaskVehicleCardStyles: InTaskVehicleCardStyle;
  updateSetting: <K extends keyof TaskListStoredSettings>(key: K, value: TaskListStoredSettings[K]) => void;
  onVehiclesPerRowChange?: (vehiclesPerRow: 2 | 3 | 4 | 5 | 6 | 7 | 8) => void;

  // Delivery Order Details Modal
  isDeliveryOrderDetailsModalOpen: boolean;
  closeDeliveryOrderDetailsModal: () => void;
  selectedVehicleForDeliveryOrder: Vehicle | null;
  selectedTaskForDeliveryOrder: Task | null;
  
  // Task Reminder Config Modal
  isReminderConfigModalOpen: boolean;
  closeReminderConfigModal: () => void;
  selectedTaskForReminderConfig: Task | null;

  // Task Card Config Modal
  taskCardConfigModalOpen: boolean;
  setTaskCardConfigModalOpen: (open: boolean) => void;
  taskCardConfig: TaskCardConfig;
  handleTaskCardConfigChange: (config: TaskCardConfig) => void;

  // Group Config Modal
  isGroupConfigModalOpen: boolean;
  setIsGroupConfigModalOpen: (open: boolean) => void;
  groupConfig: TaskGroupConfig;
  handleOpenGroupConfig: (config: TaskGroupConfig) => void;

  // Context Menus
  isTaskContextMenuOpen: boolean;
  taskContextMenuPosition: { x: number; y: number } | null;
  contextMenuTaskData: { taskId: string } | null;
  closeTaskContextMenu: () => void;
  openTankerNoteModal: (task: Task) => void;
  openReminderConfigModal: (task: Task) => void;
  filteredTasks: Task[];
  isVehicleCardContextMenuOpen: boolean;
  vehicleCardContextMenuPosition: { x: number; y: number } | null;
  vehicleCardContextMenuContext: { vehicle: Vehicle; task: Task } | null;
  closeVehicleCardContextMenu: () => void;
}

export function TaskListModalManager({
  // Tanker Note Modal
  isTankerNoteModalOpen,
  closeTankerNoteModal,
  selectedTaskForTankerNote,

  // Column Visibility Modal
  isColumnVisibilityModalOpen,
  closeColumnVisibilityModal,
  allColumns,
  columnVisibility,
  handleColumnVisibilityChange,
  currentOrder,
  handleColumnOrderChange,

  // Column Specific Style Modal
  isColumnSpecificStyleModalOpen,
  closeColumnSpecificStyleModal,
  editingColumnDef,
  columnTextStyles,
  columnBackgrounds,
  handleColumnTextStyleChange,
  handleColumnBackgroundChange,

  // Vehicle Card Styler Modal
  isStyleEditorModalOpen,
  closeStyleEditorModal,
  inTaskVehicleCardStyles,
  updateSetting,
  onVehiclesPerRowChange,

  // Delivery Order Details Modal
  isDeliveryOrderDetailsModalOpen,
  closeDeliveryOrderDetailsModal,
  selectedVehicleForDeliveryOrder,
  selectedTaskForDeliveryOrder,
  
  // Task Reminder Config Modal
  isReminderConfigModalOpen,
  closeReminderConfigModal,
  selectedTaskForReminderConfig,

  // Task Card Config Modal
  taskCardConfigModalOpen,
  setTaskCardConfigModalOpen,
  taskCardConfig,
  handleTaskCardConfigChange,

  // Group Config Modal
  isGroupConfigModalOpen,
  setIsGroupConfigModalOpen,
  groupConfig,
  handleOpenGroupConfig,

  // Context Menus
  isTaskContextMenuOpen,
  taskContextMenuPosition,
  contextMenuTaskData,
  closeTaskContextMenu,
  openTankerNoteModal,
  openReminderConfigModal,
  filteredTasks,
  isVehicleCardContextMenuOpen,
  vehicleCardContextMenuPosition,
  vehicleCardContextMenuContext,
  closeVehicleCardContextMenu,
}: TaskListModalManagerProps) {
  return (
    <>
      {/* Main Task List Modals */}
      <TaskListModals
        isTankerNoteModalOpen={isTankerNoteModalOpen}
        closeTankerNoteModal={closeTankerNoteModal}
        selectedTaskForTankerNote={selectedTaskForTankerNote}
        isColumnVisibilityModalOpen={isColumnVisibilityModalOpen}
        closeColumnVisibilityModal={closeColumnVisibilityModal}
        allColumns={allColumns}
        columnVisibility={columnVisibility}
        handleColumnVisibilityChange={handleColumnVisibilityChange}
        currentOrder={currentOrder}
        handleColumnOrderChange={handleColumnOrderChange}
        isColumnSpecificStyleModalOpen={isColumnSpecificStyleModalOpen}
        closeColumnSpecificStyleModal={closeColumnSpecificStyleModal}
        editingColumnDef={editingColumnDef}
        columnTextStyles={columnTextStyles}
        columnBackgrounds={columnBackgrounds}
        handleColumnTextStyleChange={handleColumnTextStyleChange}
        handleColumnBackgroundChange={handleColumnBackgroundChange}
        isStyleEditorModalOpen={isStyleEditorModalOpen}
        closeStyleEditorModal={closeStyleEditorModal}
        inTaskVehicleCardStyles={inTaskVehicleCardStyles}
        updateSetting={updateSetting}
        isDeliveryOrderDetailsModalOpen={isDeliveryOrderDetailsModalOpen}
        closeDeliveryOrderDetailsModal={closeDeliveryOrderDetailsModal}
        selectedVehicleForDeliveryOrder={selectedVehicleForDeliveryOrder}
        selectedTaskForDeliveryOrder={selectedTaskForDeliveryOrder}
        isReminderConfigModalOpen={isReminderConfigModalOpen}
        selectedTaskForReminderConfig={selectedTaskForReminderConfig}
        closeReminderConfigModal={closeReminderConfigModal}
        onVehiclesPerRowChange={onVehiclesPerRowChange}
      />
      
      {/* Task Card Config Modal */}
      <TaskCardConfigModal
        open={taskCardConfigModalOpen}
        onOpenChange={setTaskCardConfigModalOpen}
        config={taskCardConfig}
        onConfigChange={handleTaskCardConfigChange}
      />

      {/* Group Config Modal */}
      <TaskGroupConfigModal
        isOpen={isGroupConfigModalOpen}
        onClose={() => setIsGroupConfigModalOpen(false)}
        groupConfig={groupConfig}
        onUpdateConfig={handleOpenGroupConfig}
      />

      {/* Context Menus */}
      <TaskListContextMenus
        isTaskContextMenuOpen={isTaskContextMenuOpen}
        taskContextMenuPosition={taskContextMenuPosition}
        contextMenuTaskData={contextMenuTaskData}
        closeTaskContextMenu={closeTaskContextMenu}
        openTankerNoteModal={openTankerNoteModal}
        openReminderConfigModal={openReminderConfigModal}
        filteredTasks={filteredTasks}
        isVehicleCardContextMenuOpen={isVehicleCardContextMenuOpen}
        vehicleCardContextMenuPosition={vehicleCardContextMenuPosition}
        vehicleCardContextMenuContext={vehicleCardContextMenuContext}
        closeVehicleCardContextMenu={closeVehicleCardContextMenu}
      />
    </>
  );
}
